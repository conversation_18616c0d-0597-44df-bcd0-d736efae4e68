$(document).ready(function () {
    if (localStorage.getItem('wlc') == 1) {

        $(".bienvenida").fadeIn("slow");
        $(".bienvenida").html("¡¡Bienvenido a la plataforma Operador Movilbox!!");
        $(".bienvenida").delay(5000).fadeOut("slow");
        localStorage.setItem('wlc', 0);

    }

    l = Ladda.create(document.querySelector('button[type=submit]'));
    mostrar_noticias();

    $("#content-noticia").on('click', '#vermasnoticia', function (event) {
        $("#myTituloNoticia").html(unescape($(this).attr('data-title')));
        $("#timagen").attr("src", $(this).attr('data-imagen'));
        $("#tdescripcion").html(unescape($("#descrip" + $(this).attr('data-id')).html()));

    });

    $("#form_busqueda").submit(function (event) {
        event.preventDefault();
        mostrar_noticias();
    });
});

async function mostrar_noticias() {
    $("#content-noticia").html("");
    fetch('modulos/principal/controlador.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            accion: 'noticias',
            param: $("#param_busq").val()
        })
    }).then(response => response.json()).then(data => {
        if (data.length == 0) {
            $("#myCarousel").html(`<div class='text-center'>No se encontraron noticias</div>`)
        } else {
            $("#myCarousel").html(`<!-- Indicadores -->
                <ol class="carousel-indicators">
                </ol>
            
                <!-- Contenedor de los slides -->
                <div class="carousel-inner">

                </div>
            
                <!-- Controles -->
                <a class="left carousel-control" href="#myCarousel" data-slide="prev">
                <span class="glyphicon glyphicon-chevron-left"></span>
                </a>
                <a class="right carousel-control" href="#myCarousel" data-slide="next">
                <span class="glyphicon glyphicon-chevron-right"></span>
                </a>`)
            generar_noticias(data)
        }
    });
}

function limitar_caracter(string, lengCaracter) {
    if (string.length > lengCaracter) {
        string = string.substr(0, lengCaracter) + ' ...';
    }
    return string;
}

function generar_noticias(datos) {
    // Generación dinámica del carrusel
    const carouselInner = document.querySelector('.carousel-inner');
    let itemsPerSlide = 3;
    let carouselItem;

    datos.forEach((data, index) => {

        let tipoNoticia = "Noticia";
        let opac = "";
        let descripcion = "";
        let content_img = "";
        let btn_file = "";
        const titulo = hex2a(data.titulo)
        const tags = data.palabras_claves.split(",");
        const urlRaiz = "../files_noti/imgs/"
        let targetURL = data.url

        // Si el esquema no es http, ftp, o https, agregamos "http://"
        if (!/^https?:\/\/|^ftp:\/\//i.test(targetURL)) {
            targetURL = 'http://' + targetURL;
        }

        if (data.tipo != 1) {
            tipoNoticia = "Promoción";
        }


        if ((data.leido == 0 || data.leido == null) || (data.leido == 0 && data.leido_mo == 0 && data.estado_mo == 0) || (data.leido == 1 && data.leido_mo == 0 && data.estado_mo == 1)) {
            opac = "opacity:0.2; float: right;";
        } else if ((data.leido == 1 && data.leido_mo == 0 && data.estado_mo == 0) || (data.leido == 1 && data.leido_mo == 1 && data.estado_mo == 1)) {
            opac = "color: lightgreen; opacity:1; float: right;";
        }

        if (data.descripcion_texto != undefined) {
            descripcion += hex2a(data.descripcion_texto)
        } else {
            descripcion += limitar_caracter("Sin descripcion...", 400);
        }

        if (data.ruta_file != "") {
            btn_file = `<button class="btn btn-primary verPdf btn-success" data-toggle="modal" onclick="verDetalle(1, '${data.id}')" data-id="${data.id}" data-target="#modalPdf">
                            <i class="fa fa-file-pdf-o"></i> Ver Archivo
                        </button>`
        }

        if (data.ruta_img != "") {
            //data.ruta_img = 'https://cdn.pixabay.com/photo/2023/08/21/23/09/los-angeles-8205184_640.jpg'
            content_img = `<div class="content-img">
                          <img src="${urlRaiz}${data.ruta_img}" class="card-img-top crop" alt="${titulo}">
                      </div>`
        } else {
            content_img = `<div class="content-img">
                        <div class="img-tag">
                            <img src="modulos/principal/not_found.svg" alt="Sin imágen">
                        </div>
                      </div>`
        }

        if (index % itemsPerSlide === 0) {
            carouselItem = document.createElement('div');
            carouselItem.className = `item ${index === 0 ? 'active' : ''}`;
            carouselInner.appendChild(carouselItem);
        }

        const card = document.createElement('div');
        card.className = 'col-md-4 nopadding';  // Distribución para 3 elementos en pantalla

        card.innerHTML = `<div class="content-carusel">
                          <div>
                            <i class="fa fa-check-circle pull-right" aria-hidden="true" style="${opac}"></i>
                          </div>
                          ${content_img}
                          <div class="card-content contenido">
                              <div class="card-title">
                              <span class="card-title-text">${titulo}</span> - <small class="text-muted">${tipoNoticia}</small>
                              </div>
                              <p class="card-text"></p>
                              <div class="content-description">
                                <a href="${targetURL}" target="_blank">${data.url}</a>
                              </div>
                              <div class="content-description">
                                <div>
                                    ${descripcion}
                                </div>
                              </div>
                          </div>
                          <div class="card-content footer">
                            <p class="card-text chips">${tags.map(tag => `<span class="label btn-info" style="margin: 1px;">${tag}</span>`).join(' ')}</p>
                            <p class="card-text"><small class="text-muted">${data.fecha}</small></p>
                            <button class="btn btn-primary verMas" data-toggle="modal" onclick="verDetalle(2, '${data.id}', '${data.palabras_claves}', '${data.url}', '${data.fecha}' )" data-id="${data.id}" data-target="#MasNoticia">
                                <i class="fa fa-eye"></i> Ver más
                            </button>
                            ${btn_file}
                          </div>
                      </div>`;

        carouselItem.appendChild(card);
    });

}

async function verDetalle(tipo, id, palabras_claves, url, fecha) {
    if (tipo == 1) {
        var Noti = id
        fetch('modulos/principal/controlador.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                accion: 'detalles_noticias',
                id: Noti
            })
        }).then(response => response.json()).then(data => {
            var file = data[0]['ruta_file'];
            var titulo = hex2a(data[0]['titulo']);
            $("#miTitlePdf").html(titulo);
            $('#filePdf').attr("data", "../files_noti/files/" + file);
        });
    } else if (tipo == 2) {
        var Noti = id
        fetch('modulos/principal/controlador.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                accion: 'detalles_noticias',
                id: Noti
            })
        }).then(response => response.json()).then(data => {
            const tags = palabras_claves.split(",");
            let targetURL = url

            // Si el esquema no es http, ftp, o https, agregamos "http://"
            if (!/^https?:\/\/|^ftp:\/\//i.test(targetURL)) {
                targetURL = 'http://' + targetURL;
            }
            let html = `<p><a href="${targetURL}" target="_blank">${url}</a></p>
                            <p class="card-text chips">${tags.map(tag => `<span class="label btn-info" style="margin: 1px;">${tag}</span>`).join(' ')}</p>
                            <p>${fecha}</p>`

            var tipo = data[0]['tipo'];
            var tipoN = "";

            if (tipo == 1) {
                tipoN = "Noticia";
            } else {
                tipoN = "Promoción";
            }

            var titulo = data[0]['titulo'];
            var imagen = data[0]['ruta_img'];
            var descripcion = hex2a(data[0]['descripcion']);
            let img = ''
            if (imagen != "") {
                img = `<img src="../files_noti/imgs/${imagen}" style="width: 40%; height: auto; margin:5px; float: left;" class="img-thumbnail">`
            }

            descripcion = img + descripcion
            $('#myTituloNoticia').html("<strong>" + tipoN + "</strong> - " + hex2a(titulo));
            $('#tdescripcion').html(descripcion);
            $("#check_" + Noti).attr('style', '');
            $("#check_" + Noti).attr('style', 'opacity:1; float: right; color:lightgreen;');
            $("#footer_detalle").html(html)
            notificaciones();
        });
    }

}

function delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
}