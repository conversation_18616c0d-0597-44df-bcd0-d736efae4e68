<?php

include_once("../../config/mainController.php"); // Incluye el Controlador Principal
include_once("../../modulos/crear_catalogos/modelo.php");	// Incluye el Modelo.
$controller = new mainController; // Instancia a la clase MainController
$modelo = new crear_catalogo(); // Instancia a la clase del modelo

try {
	$metodo = $_SERVER['REQUEST_METHOD'];
	$tipo_res = "";
	$response = null;
	$variables = array();

	if (!empty($_POST)) {
		$variables = $_POST;
	} else if ($metodo == 'POST' && $_SERVER['CONTENT_TYPE'] == 'application/json') {
		// leer json
		$json = file_get_contents('php://input');
		$variables = json_decode($json, true);
	}

	// Evita que ocurra un error si no manda accion.
	if (!isset($variables['accion'])) {
		http_response_code(404);
		header("Content-type: text/plain; charset=utf-8");
		echo "0";
		return;
	}

	$accion = $variables['accion'];
	// Dependiendo de la accion se ejecutaran las tareas y se definira el tipo de respuesta.
	switch ($accion) {
		case 'Catalogos':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;
			$response = $modelo->traeCatalogos();
			break;
		case 'Referencias':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;
			$response = $modelo->traeReferencias();
			break;
		case 'ListarCatalogos':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;
			$idCata = $variables["idCata"];
			$response = $modelo->ListarCatalogos($idCata);
			break;
		case 'saveEditCata':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;
			$datos = $variables["datos"];
			$response = $modelo->saveEditCata($datos);
			break;
		case 'recibirDatos':
			$tipo_res = 'PLAIN'; //Definir tipo de respuesta;
			$response = $modelo->recibirArchivo($_FILES);
			break;
		case 'poyachu':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;
			$response = $modelo->NewCatalogo($variables, $_FILES);
			break;
		case 'eliminar':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;
			$idCata = $variables["idCata"];
			$idVis = $variables["idVis"];
			$response = $modelo->eliImagen($idCata, $idVis);
			break;
		default:
			http_response_code(404);
			header("Content-type: text/plain; charset=utf-8");
			echo "0";
			return;
	}

	// Respuestas del Controlador
	if ($tipo_res == "JSON") {
		header("Content-type: application/json; charset=utf-8");
		$json_output = json_encode($response, true); // $response será un array con los datos de nuestra respuesta.
		if (json_last_error() !== JSON_ERROR_NONE) {
			// Si hubo un error en la codificación JSON
			throw new Exception("Error al codificar la respuesta JSON: " . json_last_error_msg());
		} else {
			echo $json_output;
		}
	} elseif ($tipo_res == "HTML") {
		header("Content-type: text/html; charset=utf-8");
		echo $response; // $response será un html con el string de nuestra respuesta.
	} else {
		header("Content-type: text/plain; charset=utf-8");
		echo $response; // $response será un texto plano con el string de nuestra respuesta.
	}
} catch (Exception $e) {
	header("Content-type: application/json; charset=utf-8");
	http_response_code(500);
	echo json_encode(array("error" => true, "mensaje" => $e->getMessage()));
} catch (Error $e) {
	header("Content-type: application/json; charset=utf-8");
	http_response_code(500);
	echo json_encode(array("error" => true, "mensaje" => $e->getMessage()));
}
