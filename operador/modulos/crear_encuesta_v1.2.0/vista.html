<!-- estilos -->
<style type="text/css">
  /* sortable (inicio) */
  .moverPreguntas {
    list-style-type: none;
    margin: 0;
    padding: 0;
    width: 60%;
  }

  .moverPreguntas tr.moverPre {
    height: 56px;
  }

  .ui-state-highlight {
    height: 56px;
    background-color: #d9e7f5
  }

  /* sortable (fin) */

  .borde-circular {
    position: absolute;
    top: 13px;
    left: 365px;
    width: 110px;
    height: 28px;
    border: 0;
  }

  .circulo {
    color: #fff;
    width: 11px;
    height: 11px;
    line-height: 50px;
    font-size: 1.4em;
    text-align: center;
    position: absolute;
    background-color: #e51515;
    border-radius: 50%;
    top: 7px;
    left: 0;

    -webkit-animation-name: movimiento;
    -webkit-animation-duration: 2s;
    -webkit-animation-iteration-count: infinite;
    -webkit-animation-direction: alternate;

    -ms-transform: scale(1.2, 1.2);
    -webkit-transform: scale(1.2, 1.2);
    -moz-transform: scale(1.2, 1.2);
    -o-transform: scale(1.2, 1.2);
    transform: scale(1.2, 1.2);
  }

  @-webkit-keyframes movimiento {
    0% {
      left: 0;
    }

    10% {
      left: 5px;
    }

    15% {
      left: 10px;
    }

    20% {
      left: 15px;
    }

    25% {
      left: 20px;
    }

    30% {
      left: 25px;
    }

    35% {
      left: 30px;
    }

    40% {
      left: 35px;
    }

    45% {
      left: 40px;
    }

    50% {
      left: 45px;
    }

    55% {
      left: 50px;
    }

    60% {
      left: 55px;
    }

    65% {
      left: 60px;
    }

    70% {
      left: 65px;
    }

    75% {
      left: 70px;
    }

    80% {
      left: 75px;
    }

    85% {
      left: 80px;
    }

    90% {
      left: 85px;
    }

    95% {
      left: 90px;
    }

    100% {
      left: 95px;
    }
  }

  #consulta {
    position: relative;
  }

  #parent-reporte-encuesta {
    height: 34px;
    margin: 9px;
    position: absolute;
    float: right;
    right: 0;
  }

  #tbl {
    width: 100% !important;
  }

  .filter-width {
    width: calc(50% - 140px) !important;
    margin-right: 140px !important;
  }

  #tbl_filter {
    float: right;
    margin-top: -5px;
  }

  #tbl_filter input {
    border-radius: 3px;
    width: 200px;
    font-size: 16px;
    height: 32px;
  }

  table.dataTable.dtr-inline.collapsed>tbody>tr>td:first-child:before,
  table.dataTable.dtr-inline.collapsed>tbody>tr>th:first-child:before {
    top: 18px;
  }

  /* modal de asignación */
  #modal_asig #frm_con_punto .modal-body {
    padding: 10px 15px 0;
  }

  /* pasos de la asignación (inicio) */
  .pasos {
    margin: 10px 0 60px;
  }

  .pasos ol {
    padding: 0 !important;
  }

  .pasos li {
    position: relative;
    display: inline-block;
    list-style-type: none;
    width: calc(25% - 32px);
    height: 3px;
  }

  .pasos li:not(:first-child) {
    margin-left: 39px;
  }

  .pasos label {
    padding-top: 10px;
    color: white;
  }

  .pasos h5 {
    width: 375%;
    position: absolute;
    margin-left: -50px;
    text-align: center;
  }

  .nmPaso {
    content: "";
    position: absolute;
    top: 50%;
    left: calc(100% + 1px);
    bottom: 0;
    width: 40px;
    height: 40px;
    transform: translateY(-50%);
    border-radius: 50%;
    text-align: center;
  }

  .activo {
    background: #003145;
  }

  .desactivo {
    background: #bdbcbc;
  }

  /* pasos de la asignación (fin) */

  /* contenido de los pasos (inicio) */
  .contentedor-botones {
    width: 100%;
    overflow: hidden;
    text-align: center;
    margin: 0 auto;
    padding-top: 30px;
  }

  .contentedor-botones button {
    display: inline-table;
  }

  #contenedor-paso1,
  #contenedor-paso2,
  #contenedor-paso3 {
    padding: 30px 20px;
  }

  /* contenido de los pasos (fin) */

  /* asignación (inicio) */
  #deta_asig_oper_app div i,
  #det-paso2-sis-op div i {
    font-size: 16px;
    color: #10628a;
    position: relative;
    top: 3px;
    cursor: pointer;
  }

  .glyphicon-check.disabled {
    font-size: 16px;
    color: #666;
    position: relative;
    top: 3px;
  }

  #asig-paso2-distri,
  #asig-paso2-oper {
    display: none;
    padding: 5px;
    border-bottom: 1px solid #ddd;
  }

  #asig-paso2-sis {
    display: none;
  }

  /* asignación (fin) */

  /* guardando (inicio) */
  #modal_info .modal-content {
    border-radius: 4px;
  }

  /* guardando (fin) */

  #descrip_e {
    resize: none;
    min-height: 120px;
  }
</style>

<!-- contenedor -->
<div class="row">
  <div class="col-md-8 col-md-offset-2">
    <div class="panel panel-primary">
      <div class="panel-heading text-center">
        <h3 class="panel-title">Parámetros de Búsqueda</h3>
      </div>

      <form name="frm_b_noti" method="post" id="frm_b_noti" action="#">
        <div class="panel-body">
          <div class="form-group col-md-6">
            <label for="fecha_ini_b">Fecha Inicio:</label>

            <div class="input-group date">
              <input type="text" id="fecha_ini_b" name="fecha_ini_b" class="form-control" title="Fecha Inicio" />

              <span class="input-group-addon">
                <span class="glyphicon glyphicon-calendar"></span>
              </span>
            </div>
          </div>

          <div class="form-group col-md-6">
            <label for="fecha_fin_b">Fecha Fin:</label>
            <div class="input-group date">
              <input type="text" id="fecha_fin_b" name="fecha_fin_b" class="form-control" title="Fecha Fin" />

              <span class="input-group-addon">
                <span class="glyphicon glyphicon-calendar"></span>
              </span>
            </div>
          </div>

          <div class="form-group col-md-6">
            <label for="titulob">Título:</label>

            <select name="titulob" id="titulob" class="form-control">
              <option value="">Seleccionar...</option>
              <option value="1">Seleccion 1</option>
              <option value="0">Seleccion 2</option>
            </select>
          </div>

          <div class="form-group col-md-6">
            <label for="estadob">Estado:</label>

            <select name="estadob" id="estadob" class="form-control">
              <option value="">Seleccionar...</option>
              <option value="1">Activo</option>
              <option value="0">Inactivo</option>
            </select>
          </div>
        </div>

        <div class="panel-footer text-center">
          <button type="submit" name="button" id="button" class="btn btn-primary">Buscar</button>
          <button type="button" name="btn_crear" id="btn_crear" class="btn">Agregar</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- tabla con encuestas -->
<div id="consulta" style="display: none;" class="panel panel-primary">
  <div id="parent-reporte-encuesta">
  </div>

  <div class="panel-body" style="overflow-x: auto;">
    <table id="tbl" class="table table-hover table-striped dt-responsive nowrap">
      <thead>
        <tr>
          <th class="all">ÁRBOL</th>
          <th class="all">TÍTULO</th>
          <th>DESCRIPCIÓN</th>
          <th>USUARIO CREACIÓN</th>
          <th># PREGUNTAS</th>
          <th>FECHA INICIO</th>
          <th>FECHA FIN</th>
          <th>OBLIGATORIO</th>
          <th>PERMITE NAVEGAR HACIA ATRÁS</th>
          <th class="all">ESTADO</th>
          <th class="all">ELIMINAR</th>
          <th class="all">VIGENCIA</th>
          <th class="all">REPORTE DISTRIBUIDOR</th>
          <th class="all">DUPLICAR</th>
          <th class="all">AGREGAR PREGUNTA</th>
          <th class="all">EDITAR</th>
          <th class="all">ASIGNAR</th>
        </tr>
      </thead>
    </table>
  </div>
</div>

<!-- modal de la asignación de permisos -->
<div class="modal fade" id="modal_asig">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">
          <span aria-hidden="true">&times;</span>
        </button>

        <h4 class="modal-title" id="titulo_asig"></h4>
      </div>

      <form method="POST" id="frm_con_punto">
        <div class="modal-body">
          <!-- pasos -->
          <section class="pasos">
            <ol>
              <li class="selecionPasos activo" style="" id="paso1" data-placement="bottom" data-toggle="toggle">
                <div class="nmPaso selecionPasos activo" id="paso1l">
                  <label>1</label>
                  <h5>¿Quién aplica la encuesta?</h5>
                </div>
              </li>

              <li class="selecionPasos activo" id="paso2" data-placement="bottom" data-toggle="toggle">
                <div class="nmPaso selecionPasos desactivo" id="paso2l">
                  <label>2</label>
                  <h5>¿A quien se le aplica la encuesta?</h5>
                </div>
              </li>

              <li class="selecionPasos desactivo" id="paso3" data-placement="bottom" data-toggle="toggle">
                <div class="nmPaso selecionPasos desactivo" id="paso3l">
                  <label>3</label>
                  <h5>Asignación DCS</h5>
                </div>
              </li>

              <li class="selecionPasos desactivo" id="paso4"></li>
            </ol>
          </section>

          <!-- contenido de los pasos -->
          <div class="row">
            <!-- paso 1 -->
            <div class="col-md-8 col-md-offset-2" id="contenedor-paso1">
              <div class="col-md-12">
                <!-- operador (app) -->
                <div id="asig_oper_app" style="padding: 5px; border-bottom: 1px solid #ddd;">
                  <div style="float: left;">
                    <i class="glyphicon glyphicon-unchecked oper_app"
                      style="font-size: 16px; color: #10628a; position: relative; top: 3px; cursor: pointer;"></i>
                    <span>Operador</span>
                  </div>

                  <div style="position: absolute; cursor: pointer; right: 14px;">
                    <i class="glyphicon glyphicon-minus indi_asig_oper_app"
                      style="font-size: 12px; color: #10628a; position: relative; cursor:pointer;"></i>
                  </div>

                  <br />

                  <div id="deta_asig_oper_app" style="position: relative; left: 20px;"></div>
                </div>

                <!-- distribuidor (app) -->
                <div id="asig_pdv" style="padding: 5px;border-bottom: 1px solid #ddd;">
                  <div style="float: left;">
                    <i class="glyphicon glyphicon-unchecked app-pdv"
                      style="font-size: 16px; color: #10628a; position: relative; top: 3px; cursor:pointer;"></i>
                    <span>Distribuidor</span>
                  </div>

                  <div style="position: absolute; cursor: pointer; right: 14px;">
                    <i class="glyphicon glyphicon-minus indi_app_pdv"
                      style="font-size: 12px; color: #10628a; position: relative; cursor:pointer;"></i>
                  </div>

                  <br />

                  <div id="ver_deta_app_pdv" style="position: relative; left: 20px;">
                    <div>
                      <i class="glyphicon glyphicon-unchecked app-vende-pdv" id="vendedor"
                        style="font-size: 16px; color: #10628a; position: relative; top: 3px; cursor: pointer;"></i>
                      <span>Vendedor</span>
                    </div>

                    <div>
                      <i class="glyphicon glyphicon-unchecked app-super-pdv" id="supervisor"
                        style="font-size: 16px; color: #10628a; position: relative; top: 3px; cursor: pointer;"></i>
                      <span>Supervisor</span>
                    </div>
                  </div>
                </div>

                <!-- sistema -->
                <div id="asig_sistema" style="padding: 5px;">
                  <div style="float: left;">
                    <i class="glyphicon glyphicon-unchecked check_sistema"
                      style="font-size: 16px;color: #10628a;position: relative;top: 3px;cursor:pointer;"></i> Sistema
                  </div>
                </div>
              </div>

              <div class="contentedor-botones">
                <button type="button" class="btn btn-primary" id="siguiente-paso1">Siguiente</button>
              </div>
            </div>

            <!-- paso 2 -->
            <div class="col-md-8 col-md-offset-2" id="contenedor-paso2" style="display: none;">
              <div class="col-md-12">
                <!-- operador -->
                <div id="asig-paso2-oper">
                  <b>Operador</b>

                  <div style="position: relative; left: 20px;">
                    <div>
                      <i class="glyphicon glyphicon-check disabled"></i> <span>PDV</span>
                    </div>
                  </div>
                </div>

                <!-- operador -->
                <div id="asig-paso2-distri">
                  <b>Distribuidor</b>

                  <div style="position: relative; left: 20px;">
                    <div>
                      <i class="glyphicon glyphicon-check disabled"></i> <span>PDV</span>
                    </div>
                  </div>
                </div>

                <!-- sistema -->
                <div id="asig-paso2-sis">
                  <b>Sistema</b>

                  <div id="det-paso2-sis" style="position: relative; left: 20px;">
                    <!-- operador -->
                    <div id="paso2-sis-op" style="padding: 5px; border-bottom: 1px solid #ddd;">
                      <div style="float: left;">
                        <b>Operador</b>
                      </div>

                      <div style="position: absolute; cursor: pointer; right: 14px;">
                        <i class="glyphicon glyphicon-minus indi_paso2_op"
                          style="font-size: 12px; color: #10628a; position: relative; cursor: pointer;"></i>
                      </div>

                      <br />

                      <div id="det-paso2-sis-op" style="position: relative; left: 20px;"></div>
                    </div>

                    <!-- distribuidor -->
                    <div id="paso2-sis-di" style="padding: 5px; border-bottom: 1px solid #ddd;">
                      <div style="float: left;">
                        <b>Distribuidor</b>
                      </div>

                      <div style="position: absolute; cursor: pointer; right: 14px;">
                        <i class="glyphicon glyphicon-minus indi_app"
                          style="font-size: 12px; color: #10628a; position: relative; cursor: pointer;"></i>
                      </div>

                      <br />

                      <div id="ver_deta_app" style="position: relative; left: 20px;">
                        <div>
                          <i class="glyphicon glyphicon-unchecked app-usuario_sistema"
                            style="font-size: 16px; color: #10628a; position: relative; top: 3px; cursor: pointer;"></i>
                          <span>Usuario del Sistema</span>
                        </div>

                        <div>
                          <i class="glyphicon glyphicon-unchecked app-vende"
                            style="font-size: 16px;color: #10628a; position: relative; top: 3px;cursor: pointer;"></i>
                          <span>Vendedor</span>
                        </div>

                        <div>
                          <i class="glyphicon glyphicon-unchecked app-super"
                            style="font-size: 16px;color: #10628a; position: relative; top: 3px;cursor: pointer;"></i>
                          <span>Supervisor</span>
                        </div>
                      </div>
                    </div>

                    <!-- punto de venta -->
                    <div>
                      <i class="glyphicon glyphicon-unchecked sis-pdv"
                        style="font-size: 16px; color: #10628a; position: relative; top: 3px; cursor:pointer;"></i>
                      <span>PDV</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="contentedor-botones">
                <button type="button" class="btn btn-primary" id="atras-paso2">Atrás</button>
                <button type="button" class="btn btn-primary" id="siguiente-paso2">Siguiente</button>
              </div>
            </div>

            <!-- paso 3 -->
            <div class="col-md-8 col-md-offset-2" id="contenedor-paso3" style="display: none;">
              <div style="border-bottom: 1px solid #c0c0c0; margin-bottom: 4px; padding-bottom: 4px;">
                <i class="glyphicon glyphicon-unchecked zonales_check"
                  style="font-size: 16px; color: #10628a; position: relative; top: 3px; cursor: pointer;"></i>
                <span style="font-weight: bold; font-size: 15px;">Regionales</span>
              </div>

              <div class="col-md-12" id="contenido_dcs"></div>

              <div class="contentedor-botones">
                <button type="button" class="btn btn-primary" id="atras-paso3">Atrás</button>
                <button type="button" class="btn btn-primary" id="guardar_asignacion">Guardar</button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- modal crear, editar o duplicar encuesta -->
<div class="modal fade" id="modal_crud_encuesta">
  <div class="modal-dialog modal-lm">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">
          <span aria-hidden="true">&times;</span>
        </button>

        <h4 class="modal-title" id="titulo_noti_promo"></h4>
      </div>

      <form method="POST" id="frm_crud">
        <div class="modal-body adminlist">
          <div class="row">
            <div class="col-md-12">
              <div class="form-group col-md-12">
                <label for="titulo">Título:</label>
                <input type="text" name="titulo" id="titulo" class="form-control" placeholder="Titulo"
                  required="required" maxlength="300">
              </div>

              <div class="form-group col-md-12">
                <label for="descrip_e">Descripción: </label>
                <textarea name="descrip_e" id="descrip_e" class="form-control" placeholder="Descripción"
                  required="required" maxlength="1000"></textarea>
              </div>

              <div class="form-group col-md-12">
                <label for="fecha_ini">Fecha Inicio:</label>

                <div class="input-group date">
                  <input type="text" id="fecha_ini" name="fecha_ini" class="form-control" title="Fecha Inicio"
                    required="required">

                  <span class="input-group-addon">
                    <span class="glyphicon glyphicon-calendar"></span>
                  </span>
                </div>
              </div>

              <div class="form-group col-md-12">
                <label for="fecha_fin">Fecha Fin:</label>

                <div class="input-group date">
                  <input type="text" id="fecha_fin" name="fecha_fin" class="form-control" title="Fecha Fin"
                    required="required">

                  <span class="input-group-addon">
                    <span class="glyphicon glyphicon-calendar"></span>
                  </span>
                </div>
              </div>

              <div class="form-group col-md-12">
                <label>Obligatorio: </label><br />
                <input type="radio" name="c_obliga" id="c_obliga_1" value="1" required="required">
                <label for="c_obliga_1">Si</label><br />
                <input type="radio" name="c_obliga" id="c_obliga_0" value="0" required="required">
                <label for="c_obliga_0">No</label><br />
              </div>

              <div class="form-group col-md-12">
                <label>Permitir navegar hacia atrás:</label><br />
                <input type="radio" name="c_navega" id="c_navega_1" value="1" required="required">
                <label for="c_navega_1">Si</label><br />
                <input type="radio" name="c_navega" id="c_navega_0" value="0" required="required">
                <label for="c_navega_0">No</label><br />
              </div>

              <div class="form-group col-md-12">
                <label>Aplicar la encuesta:</label><br />
                <input type="radio" name="aplicar_encuesta" id="vez_vigencia" value="0" required="required">
                <label for="vez_vigencia">Una vez por vigencia</label><br />
                <input type="radio" name="aplicar_encuesta" id="cada_visita" value="1" required="required">
                <label for="cada_visita">Cada vez que se visite</label><br />
              </div>

              <div class="form-group col-md-12">
                <label>Permitir visualizar el reporte al Distribuidor:</label><br />
                <input type="radio" name="reporte_distri" id="reporte_distri_1" value="1" required="required">
                <label for="reporte_distri_1">Si</label><br />
                <input type="radio" name="reporte_distri" id="reporte_distri_0" value="0" required="required">
                <label for="reporte_distri_0">No</label><br />
              </div>

            </div>
          </div>

          <div class="modal-footer">
            <button type="button" class="btn" data-dismiss="modal">Cancelar</button>
            <button type="submit" class="btn btn-primary" id="btn_accion"></button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

<div class="modal fade" id="modal_crud_preguntas">
  <div class="modal-dialog modal-lm">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="titulo_pregunta">Título Modal</h4>
      </div>

      <form method="post" id="frm_crud_preguntas" action="#">
        <div class="modal-body adminlist">
          <div class="row">
            <div class="col-md-12">

              <div style="display: block;" class="form-group col-md-12">
                <div id="ramaEncuesta"></div>
              </div>

              <div id="ver_opciones_duplicar" style="display: none;" class="col-md-12">
                <label>Seleccionar Encuesta Destino: </label>
                <div class="col-md-12" style="border: 2px solid #a7a5a5;">
                  <div class="form-group col-md-12">
                    <label for="seleccionar_encuesta">Selecciona una encuesta: </label>
                    <select name="seleccionar_encuesta" id="seleccionar_encuesta" class="form-control"></select>
                  </div>

                  <div class="form-group col-md-12">
                    <label>Selecciona una pregunta: (Opcional) </label>
                    <select name="seleccionar_pregunta" id="seleccionar_pregunta" class="form-control"></select>
                  </div>

                  <div class="form-group col-md-12">
                    <label>Selecciona una respuesta: (Opcional) </label>
                    <select name="seleccionar_respuesta" id="seleccionar_respuesta" class="form-control"></select>
                  </div>
                </div>
              </div>

              <div class="form-group col-md-12">
                <label>Pregunta: </label>
                <TEXTAREA name="pregunta" id="pregunta" class="form-control" placeholder="Pregunta" required="required"
                  maxlength="1000"></TEXTAREA>
              </div>

              <div class="form-group col-md-12">
                <label>Posición: </label>
                <select name="orden_pregunta" id="orden_pregunta" class="form-control" required="required">
                  <option value="">Seleccionar...</option>
                  <option value="0">Al Principio</option>
                </select>
              </div>

              <div class="form-group col-md-12">
                <label>Tipo de pregunta: </label>
                <select name="tipopc" id="tipopc" class="form-control" required="required">
                  <option value="">Seleccionar...</option>
                  <option value="1">Única</option>
                  <option value="2">Múltiple</option>
                  <option value="3">Abierta Alfanumérica</option>
                  <option value="4">Abierta Numérica</option>
                </select>
              </div>

              <div class="form-group col-md-12">
                <label>Obligatorio: </label> <br>
                <input type="radio" name="c_obligaPre" value="1" required="required"> Si<br>
                <input type="radio" name="c_obligaPre" value="0" required="required"> No<br>
              </div>

            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn" data-dismiss="modal">Cancelar</button>
            <button type="submit" class="btn btn-primary" id="btn_pregunta">Guardar</button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

<div class="modal fade" id="modal_crud_respuestas">
  <div class="modal-dialog modal-lm">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title" id="titulo_respuesta">Título Modal</h4>
      </div>

      <form method="post" id="frm_crud_respuestas" action="#">
        <div class="modal-body adminlist">
          <div class="row">
            <div class="col-md-12">

              <div style="display: block;" class="form-group col-md-12">
                <div id="ramaEncuesta2"></div>
              </div>

              <div id="ver_opciones_duplicar_respuesta" style="display: none;" class="col-md-12">
                <label>Seleccionar Encuesta Destino: </label>
                <div class="col-md-12" style="border: 2px solid #a7a5a5;">
                  <div class="form-group col-md-12">
                    <label for="seleccionar_encuesta_respuesta">Selecciona una encuesta: </label>
                    <select name="seleccionar_encuesta_respuesta" id="seleccionar_encuesta_respuesta"
                      class="form-control"></select>
                  </div>

                  <div class="form-group col-md-12">
                    <label>Selecciona una pregunta:</label>
                    <select name="seleccionar_pregunta_respuesta" id="seleccionar_pregunta_respuesta"
                      class="form-control"></select>
                  </div>
                </div>
              </div>

              <div class="form-group col-md-12">
                <label>Respuesta: </label>
                <input type="text" name="respuesta" id="respuesta" class="form-control" placeholder="Respuesta"
                  required="required">
              </div>

              <div class="form-group col-md-12">
                <label>Posición: </label>
                <select name="orden_respuesta" id="orden_respuesta" class="form-control" required="required">
                  <option value="">Seleccionar...</option>
                  <option value="0">Al Principio</option>
                </select>
              </div>

            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn" data-dismiss="modal">Cancelar</button>
            <button type="submit" class="btn btn-primary" id="btn_resp">Guardar</button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- modal de "guardando..." -->
<div class="modal" id="modal_info" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-body">
        <div class="text-center">
          <span>Guardando...</span>

          <div class="borde-circular"><span class="circulo"></span></div>
        </div>
      </div>
    </div>
  </div>
</div>